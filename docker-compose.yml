services:
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./storage:/app/storage
      - ./backend:/app/backend
    environment:
      - DATABASE_URL=sqlite:///./storage/config_analyzer.db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # 可选：使用PostgreSQL替代SQLite
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: config_analyzer
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped

volumes:
  redis_data:
  # postgres_data:
