# -*- coding: utf-8 -*-
"""
Created on Wed Mar 13 09:15:59 2024

@author: cys44995
"""

import os
import re
from datetime import datetime

#全局变量
cs=''
bjfile1=''

def make_dir(path):
#判断文件夹是否存在
    if  not os.path.isdir(path): #检查路径是否存在
        os.makedirs(path)        #创建文件夹path
        print('已经创建文件夹：' + path)
    #else:
        #print('已经存在文件夹：' + path)
    
def out(diagfile):
    #历遍诊断文件

    file = open(diagfile,mode='r',encoding='gb18030',errors='ignore')
    txt = file.read()
    #txt内有时间进行去除
    # lstxt_list = txt.split('\n')
    # txt = ''
    # for lstxt in lstxt_list:
    #     lstxt = lstxt[10:]
    #     print(lstxt)
    #     txt = txt + '\n' + lstxt
    
    #诊断文件已经写入txt
    sysname = re.findall('(?<=sysname).*?(?=\n)',txt)[0].replace(' ','').replace('/','_') #查找设备名
    print('#查找到设备：'+ sysname)
    config = re.findall('(?<=display current-configuration).*?(?=\n=======)',txt,re.S)[0]
    #查找时间
    configtime = re.findall('(?<=display clock).*?(?=\n=)',txt,re.S)[0].replace('=','')
    configtime_tmp = configtime.split('\n')
    configtime = configtime_tmp[1][-10:].replace('/','-')
    t_configtime = datetime.strptime(configtime, '%m-%d-%Y')
    configtime = t_configtime.strftime('%Y%m%d')
    print('#诊断收集时间：'+configtime) 

    #保存路径检测
    config_filepath = 'config/'+sysname+'/'
    diagfile_filepath = 'diag/'+sysname+'/'
    make_dir(config_filepath)
    make_dir(diagfile_filepath)
    #配置保存到文本        
    file = open(config_filepath +sysname+'_'+configtime +'.txt',mode='w',encoding='gb18030')    
    file.write(config)
    file.close()
    #诊断文件进行存档
    os.replace(diagfile, diagfile_filepath+sysname+'_'+configtime+'.txt')

        
def update():
    #数据更新
    files = os.listdir()
    for file in files:
        if os.path.isfile(file) is True:
            if '.py' not in file:
                if '.exe' not in file:
                    out(file) 
                
def read(path):
    print('~~读取设备：'+path+'~~')
    c_path = 'config/'+path
    files = os.listdir(c_path) #历遍文件
    new_time = 0
    for file in files:
        #查找最新的配置文件
        time = int(file.split('_')[1].replace('.txt',''))
        if time > new_time:
            new_time = time 
    print('~~读取配置日期：'+str(new_time)+'~~')
    filename =c_path+'/'+ path +'_' + str(new_time) +'.txt'  
    #写入内容并返回
    file = open(filename,mode='r',encoding='gb18030',errors='ignore')
    config_txt =file.read()
    #记录文件位置，便于比较使用
    global bjfile1
    global bjfile2
    global cs
    if cs=='':
        bjfile1 = 'E:/10-诊断信息/system/' + filename
    else:
        bjfile2 = 'E:/10-诊断信息/system/' + filename
    cs ='2'
    #返回配置明细内容
    return config_txt

def findcfg(config):
    config_list = config.split('\n#')
    while True:
        kw = input('*********************\n请输入关键词：')
        #显示全部配置
        if kw == '':
            print(config)
#========进行文本比较程序
        elif kw =='比较':
            index = 1
            files = os.listdir('config/') #历遍文件夹生成菜单
            for file in files:
                print(str(index)+'.'+file) #显示菜单
                index +=1
            filepath = input() #获取选择
            path = files[int(filepath)-1]
            read(path)
            bj(bjfile1, bjfile2)
            break                       
        #关键词查找程序    
        else:
            for config in config_list:
                if kw in config:
                    print(config)
                    print('#')

def bj(a,b):
    cmd = 'BCompare.exe /fv="Folder Compare" "'+a+'" "'+b+'"'
    print('~打开程序：BCompare成功！')
    os.system(cmd)



        
print('0.数据更新')
print('b.新旧文本比较')
print('p.打开路径文件夹')
index = 1
files = os.listdir('config/') #历遍文件夹生成菜单
for file in files:
    print(str(index)+'.'+file) #显示菜单
    index +=1
    
filepath = input() #获取选择
if filepath == '0':
    update()
elif filepath == '':
    update()
elif filepath == 'b':
    print('*********************\n进行文本比较')
elif filepath == 'p':
    print('0.程序文件夹')
    print('1.诊断文件夹')
    print('2.配置文件夹')
    wjjid = input() #获取选择
    file = open('sys\路径.txt',mode='r',encoding='gb18030',errors='ignore')
    wjlj = file.read()
    if wjjid == '1':
        wjlj = wjlj+'diag'
    elif wjjid == '2':
        wjlj = wjlj+'config'
    print('打开文件夹：'+wjlj)    
    os.system("explorer.exe %s" % wjlj)
    
    
else:
    path = files[int(filepath)-1]
    config = read(path)
    findcfg(config)
input('任意输入结束！')





















                    
                