#!/bin/bash

# 网络设备配置分析器启动脚本

echo "🚀 启动网络设备配置分析器 v2.0"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建存储目录..."
mkdir -p storage/uploads storage/config storage/diag

# 复制环境配置文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "🌐 访问地址："
    echo "   Web界面: http://localhost:8000"
    echo "   API文档: http://localhost:8000/docs"
    echo ""
    echo "📖 使用说明："
    echo "   1. 访问Web界面开始使用"
    echo "   2. 上传网络设备诊断文件(.txt格式)"
    echo "   3. 查看设备配置和搜索功能"
    echo "   4. 使用配置比较功能分析差异"
    echo ""
    echo "🛑 停止服务: docker-compose down"
    echo "📊 查看日志: docker-compose logs -f"
else
    echo "❌ 服务启动失败，请检查日志："
    docker-compose logs
fi
