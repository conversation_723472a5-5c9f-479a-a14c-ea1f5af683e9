# 多阶段构建 - 前端构建阶段
FROM node:18-alpine as frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ ./
RUN npm run build

# 后端运行阶段
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制后端依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端代码
COPY backend/ ./backend/

# 复制前端构建结果
COPY --from=frontend-builder /app/frontend/build ./static

# 创建必要的目录
RUN mkdir -p storage/uploads storage/config storage/diag

# 设置环境变量
ENV PYTHONPATH=/app
ENV DATABASE_URL=sqlite:///./storage/config_analyzer.db

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
